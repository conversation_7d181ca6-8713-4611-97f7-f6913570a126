../../Scripts/openai.exe,sha256=GIhD3jWw5PMYJ1QTSERowaEis0-tpWS3s_w_hUzLwRU,106369
openai-0.27.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai-0.27.2.dist-info/LICENSE,sha256=vLo94hSFHM5G7Vr0LWaYBEYW7qzoh8MjG8eiBHSrY54,1083
openai-0.27.2.dist-info/METADATA,sha256=LWCRBj1UaVCzX1lq2yHsOF4sDHPTGL718vRDIC_TYt8,13127
openai-0.27.2.dist-info/RECORD,,
openai-0.27.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai-0.27.2.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
openai-0.27.2.dist-info/entry_points.txt,sha256=q68fD1MsMkiejaG68n2mKmbuZEwWkq89oniOBBq9fo0,55
openai-0.27.2.dist-info/top_level.txt,sha256=yroxRDiE4kOdI0tEpF1d7ffoQJMxe4i3z_pKoyou9wg,7
openai-0.27.2.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
openai/__init__.py,sha256=NKBGPoh7HP3XXo2VgSfqkovavDHOPjC6G0GPGBvY3mo,2203
openai/__pycache__/__init__.cpython-39.pyc,,
openai/__pycache__/_openai_scripts.cpython-39.pyc,,
openai/__pycache__/api_requestor.cpython-39.pyc,,
openai/__pycache__/cli.cpython-39.pyc,,
openai/__pycache__/datalib.cpython-39.pyc,,
openai/__pycache__/embeddings_utils.cpython-39.pyc,,
openai/__pycache__/error.cpython-39.pyc,,
openai/__pycache__/object_classes.cpython-39.pyc,,
openai/__pycache__/openai_object.cpython-39.pyc,,
openai/__pycache__/openai_response.cpython-39.pyc,,
openai/__pycache__/upload_progress.cpython-39.pyc,,
openai/__pycache__/util.cpython-39.pyc,,
openai/__pycache__/validators.cpython-39.pyc,,
openai/__pycache__/version.cpython-39.pyc,,
openai/__pycache__/wandb_logger.cpython-39.pyc,,
openai/_openai_scripts.py,sha256=-SYV_h-4pTfyG4IB4YLUcejX7QrHZGSvS8djkbprR34,2047
openai/api_requestor.py,sha256=l0TU4WiMN1ffE3WMZPOC2bR4YTF8V-HsToMrSvhDyZo,22828
openai/api_resources/__init__.py,sha256=S5vOHXDRZu1kHikYR82kF5QUU4jEK7rMr2vEaoHQn20,907
openai/api_resources/__pycache__/__init__.cpython-39.pyc,,
openai/api_resources/__pycache__/audio.cpython-39.pyc,,
openai/api_resources/__pycache__/chat_completion.cpython-39.pyc,,
openai/api_resources/__pycache__/completion.cpython-39.pyc,,
openai/api_resources/__pycache__/customer.cpython-39.pyc,,
openai/api_resources/__pycache__/deployment.cpython-39.pyc,,
openai/api_resources/__pycache__/edit.cpython-39.pyc,,
openai/api_resources/__pycache__/embedding.cpython-39.pyc,,
openai/api_resources/__pycache__/engine.cpython-39.pyc,,
openai/api_resources/__pycache__/error_object.cpython-39.pyc,,
openai/api_resources/__pycache__/file.cpython-39.pyc,,
openai/api_resources/__pycache__/fine_tune.cpython-39.pyc,,
openai/api_resources/__pycache__/image.cpython-39.pyc,,
openai/api_resources/__pycache__/model.cpython-39.pyc,,
openai/api_resources/__pycache__/moderation.cpython-39.pyc,,
openai/api_resources/abstract/__init__.py,sha256=ZUZv2MkEpRUeRZ0NtRHczXNAamFTUK17z87O3WpXzLQ,540
openai/api_resources/abstract/__pycache__/__init__.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/api_resource.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/createable_api_resource.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/deletable_api_resource.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/engine_api_resource.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/listable_api_resource.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/nested_resource_class_methods.cpython-39.pyc,,
openai/api_resources/abstract/__pycache__/updateable_api_resource.cpython-39.pyc,,
openai/api_resources/abstract/api_resource.py,sha256=wzCww3boKgr0Ru33CIRi8lOCsMEtQJLKlz1HMHIpMkY,5443
openai/api_resources/abstract/createable_api_resource.py,sha256=w6atKaf0aUDe3NuV-wLtbmCxCGPtCxXIkH98yk8s7xQ,2573
openai/api_resources/abstract/deletable_api_resource.py,sha256=9jI4tt7RejbO0aIyLJwivyg3jclr7r55-l50mVFC7GI,1624
openai/api_resources/abstract/engine_api_resource.py,sha256=GGj9NpJkPqKozgTT3E2meIS1VsRq8YnTg_A3Uzh-P00,10007
openai/api_resources/abstract/listable_api_resource.py,sha256=-okp69Lu1OaA9aIkEK2nC-JXRW44vtCKFCN89w_4f60,2678
openai/api_resources/abstract/nested_resource_class_methods.py,sha256=7rCNmuZhLgbN7M6PcNT-P9W5taHFZF-ObDYXz7GLFuI,5086
openai/api_resources/abstract/updateable_api_resource.py,sha256=cQQd04fI85DTYpOqM_LFuZ9ldDKrkT-MRetkDG5FUU0,534
openai/api_resources/audio.py,sha256=0ln2wO0IEfQoCoeVi10BSaig1X8k_kTzrHEG7immm8w,5690
openai/api_resources/chat_completion.py,sha256=8mQ-UPp9FcJTMq2ae9nsTpLHnjEjSR7T788l-S6jQIQ,1587
openai/api_resources/completion.py,sha256=EDgsMEJXwYyTqB5fGy2Vi6nQeproKZWvNmkdNve5Ykc,1610
openai/api_resources/customer.py,sha256=vX1reXMqDHLssmT2aGev83TIE4NbnzvQ73yrQWyDpFk,539
openai/api_resources/deployment.py,sha256=9K64JZyWxVcvutebf0qguYrSRPb0V_lMALwNsWRqNzs,4055
openai/api_resources/edit.py,sha256=HlE11SFBBJH_ompIBHAYToNc4tIdUu01tZyym7f8LlE,1963
openai/api_resources/embedding.py,sha256=wbmwNYb6V7_tWJYd1h487YwltIZ1xl2eZhITInqNP-k,3387
openai/api_resources/engine.py,sha256=_bzICgcNFmpdm08yL_W3n-vekkO0AGcCdPGl352HMT8,1665
openai/api_resources/error_object.py,sha256=gKtXIo8mYZDPE2GiJUzQ8LDzLKKEPLqQoyYo_AAqOA4,892
openai/api_resources/experimental/__init__.py,sha256=fLUruA1uO11wqsDNuAKxgx3qVhQyPYC79JoZNTCIq1A,104
openai/api_resources/experimental/__pycache__/__init__.cpython-39.pyc,,
openai/api_resources/experimental/__pycache__/completion_config.cpython-39.pyc,,
openai/api_resources/experimental/completion_config.py,sha256=7vlxmFIkpleNCEUir50NBk31XxnqL0yBFZ5Ah1h9TpQ,274
openai/api_resources/file.py,sha256=TX9xFoWeAtRAGyHUTJhcI3BnpQtZKHlAW35mT6fPrGQ,7737
openai/api_resources/fine_tune.py,sha256=uotwurKyV8wAnyw4x01O2b2t3b--8i_EBSBVbV-2EhE,5263
openai/api_resources/image.py,sha256=-2Ta2Kbb5zk8wR6pOcMUQnCQiwTZlu7BlvHYWSRsgWk,6267
openai/api_resources/model.py,sha256=BiXo7j4P6-rhZW8my4eAbD6aIdQ6sAf2Dw9e-U21b_Q,169
openai/api_resources/moderation.py,sha256=XLEVJbP2A5AqcnelbIcXq7-PMqvfPPibOetOf2xiMTA,1376
openai/cli.py,sha256=ydhHJk-eUdD-4-xltN2iKmH5Us06ushLWxadexK1Mv8,37805
openai/datalib.py,sha256=Wec28jxx8bzpkmL46NWfigjwnk4SJqOe_3zrIJmMe2U,1362
openai/embeddings_utils.py,sha256=wTC17PYHMcMnUdBwUnMkIt-YIOwRWdysHrsIYzafELk,8659
openai/error.py,sha256=ou0Smzmx-IeZN38oDc4fmNp1dS7QPx4DpbDws65tGUw,4220
openai/object_classes.py,sha256=yN2tqUdG1ATov5C1zt06cG1o_ad0o5qYfZlK4bso7eY,379
openai/openai_object.py,sha256=cw_BUglIo8F-U-vKpcEhB_X-wBvdAN4cjdlNYzxkOM0,10790
openai/openai_response.py,sha256=LQE9Hm81VsGL6UFYw9rGzlFONPNJx-46QjNVtlp_3DM,536
openai/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/__pycache__/__init__.cpython-39.pyc,,
openai/tests/__pycache__/test_api_requestor.cpython-39.pyc,,
openai/tests/__pycache__/test_endpoints.cpython-39.pyc,,
openai/tests/__pycache__/test_exceptions.cpython-39.pyc,,
openai/tests/__pycache__/test_file_cli.cpython-39.pyc,,
openai/tests/__pycache__/test_long_examples_validator.cpython-39.pyc,,
openai/tests/__pycache__/test_url_composition.cpython-39.pyc,,
openai/tests/__pycache__/test_util.cpython-39.pyc,,
openai/tests/asyncio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/asyncio/__pycache__/__init__.cpython-39.pyc,,
openai/tests/asyncio/__pycache__/test_endpoints.cpython-39.pyc,,
openai/tests/asyncio/test_endpoints.py,sha256=ko3ra_FcwlmkCzxEoVwIyOTYdSNQn7VgSqUGQ7O8kro,2362
openai/tests/test_api_requestor.py,sha256=HbMEbeaM9MA6lhmZ6dQQ2GnHS-OnqNzSn04MnFqnJY0,2354
openai/tests/test_endpoints.py,sha256=iLoJimkK6gI-AoGkb9UZ0sRN7yiqWycJXdnLlwvZbFo,2209
openai/tests/test_exceptions.py,sha256=VbKHyi7QAoLi_t6IMuJWSdSkY4zJD4ngrNRjQ89GIjQ,1156
openai/tests/test_file_cli.py,sha256=BIBCaDJ6SZsAtvNqERhEQLDutJjoejgV9GCP3tYcu1A,1418
openai/tests/test_long_examples_validator.py,sha256=fiibNigP1YFlOZvs7NdmDErC1ZFuaZQEjku6SDLMUt4,1988
openai/tests/test_url_composition.py,sha256=d3Jr-bmCFLedEYu3FTYN180WT6wBygRABW8g9R3ccsA,6506
openai/tests/test_util.py,sha256=sZYMnEMG1_1Y7feqr8e-_dcRqvQrgTNROWuusG62x4Q,797
openai/upload_progress.py,sha256=L723nchd5OQ_tfDFDKOru_rlHdG73uzesOh528wOOcM,1188
openai/util.py,sha256=_kb73f0Hb6J83I7fPLQUGKxz84CcSMVPS1kD2rAwYMY,5403
openai/validators.py,sha256=GiN8mmG9-v5RdBdZRSB-0PUMGfcozKEFZ6Ft5IDfAEc,33767
openai/version.py,sha256=PWvgg1pO5onx_obdvWT0QYlTRqg_tTpSoXUCMryf9-4,19
openai/wandb_logger.py,sha256=PZxSK9w8EnyYFKEgBUUVngnxbsBOJllp3e3qncGxUFY,10361
